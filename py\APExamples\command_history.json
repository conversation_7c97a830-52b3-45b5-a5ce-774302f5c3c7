{"search_history": ["en", "e", "set-mailbox", "set-mailbo", "set-mailb", "set-mail", "set-mai", "set-ma", "set-m", "set-", "set", "se", "s", "get", "ge", "g"], "command_history": ["Get-InboxRule -Mailbox <EMAIL>", "Enable-Mailbox jbj -AutoExpandingArchive", "Get-MailboxFolderStatistics -Identity jbj | Select-Object FolderPath, FolderAndSubfolderSize, ArchivePolicy", "Add-MailboxFolderPermission -Identity <EMAIL>:\\kalender -user <EMAIL> -AccessRights editor", "Connect-MgGraph -Scopes \"User.Read.All\"", "Get-MailboxFolderPermission -Identity <EMAIL>:\\kalender", "Add-DistributionGroupMember -Identity groupName -Member userName", "Get-MgUserMemberOf -UserId \"<EMAIL>\" | Select DisplayName, Id", "Connect-MgGraph -Scopes \"User.Read.All\", \"Group.Read.All\", \"Directory.Read.All\"", "Get-MailboxFolderPermission -Identity <EMAIL>:\\kalender"]}