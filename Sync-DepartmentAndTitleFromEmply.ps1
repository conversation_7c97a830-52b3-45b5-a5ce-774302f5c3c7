# -------------------------------------------------------------------------- #
# Script: Update-AdDepartmentAndTitleFromEmply.ps1                           #
# Formål:                                                                    #
#  - Opdater AD Titel med info fra Emply                                     #
#  - Opdater AD Department med info fra Emply                                #
#  - Sender en <NAME_EMAIL> hvis der er ændringer       #
#                                                                            #
# Forfatter: <EMAIL>                                                #
# -------------------------------------------------------------------------- #

# Tjek om scriptet kører i den forbudte tidsperiode (01:00 - 05:00) - Exit hvis det er faldt indenfor denne periode
$currentTime = Get-Date
$currentHour = $currentTime.Hour

if ($currentHour -ge 1 -and $currentHour -lt 5) {
    exit 0
}

# Indstillinger
$apiKey        = "key here"
$departmentUrl = "https://api.emply.com/v1/andersen-partners/departments?apiKey=$apiKey"
$userUrl       = "https://api.emply.com/v1/andersen-partners/users?Active=true&apiKey=$apiKey"

# Email Indstillinger:
$ToAddress   = @("<EMAIL>")
$FromAddress = "<EMAIL>"
$SMTPServer  = 'andersenpartners-dk02b.mail.protection.outlook.com'

# Hashtable til mapping af department titler, hvis en bruger er en del af en den anden Emply department, så håndteres det ikke.
$departmentMap = @{
    "Drift"                      = "Drift"
    "HR"                         = "Drift"
    "Fast ejendom"               = "Fast Ejendom"
    "Insolvens"                  = "Insolvens"
    "Marketing og salg"          = "Drift"
    "Privatret"                  = "Privatret"
    "Proces"                     = "Proces"
    "Selskabsret - M&A"          = "MA"
    "Økonomi"                    = "Økonomi"
    "Sagsbehandler fast ejendom" = "Fast Ejendom"
    "IT"                         = "Drift"
}

# Hent departments & bruger info fra Emply API
try {
    $departments = Invoke-RestMethod -Uri $departmentUrl -Method Get
    $users       = Invoke-RestMethod -Uri $userUrl -Method Get
    #Write-Host "Data hentet fra Emply API succesfuldt." -ForegroundColor Green
} catch {
    #Write-Error "Fejl ved hentning af data fra Emply API: $_"
    Send-MailMessage -To $ToAddress -From $FromAddress -Subject "Fejl ved hentning af Emply data d. $(Get-Date -Format 'dd-MM-yyyy HH:mm') - Script: Sync-DepartmentAndTitleFromEmply" -BodyAsHtml "Fejl ved hentning af data fra Emply API:<br><br>$_<br><br>Mvh,<br>IT Afdelingen" -SmtpServer $SMTPServer -Encoding UTF8
    exit 1
}

# Ekskluderede emails, som ikke skal søges. Primært ejerpartnerne, da deres titel i Emply er Interessent og i AD skal det være Partner...
$excludeEmails = @("<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>"
)

# Lav et lookup for department id til title
$departmentLookup = @{}

foreach ($dep in $departments) {
    $departmentLookup[$dep.id] = $dep.title
}

# Initialiser en liste til resultaterne
$comparisonResults = @()

foreach ($user in $users) {

    # Check om email er på exclude listen
    if ($excludeEmails -contains $user.email) {
        continue
    }
    # Hop over hvis bruger ikke findes i AD
    if (-not(Get-ADUser -Filter "UserPrincipalName -eq '$($user.email)'")) {
        continue
    }

    $adUser = Get-ADUser -SearchBase "OU=Medarbejdere,OU=Users,OU=Andersen Partners,DC=int,DC=a-p,DC=dk" -Filter "UserPrincipalName -eq '$($user.email)'" -Properties Department, Title -ErrorAction SilentlyContinue

    

    if ($adUser) {
        $depTitle     = $departmentLookup[$user.departmentId]
        $adDepartment = $departmentMap[$depTitle]

        # Tjek og opdater department
        if ($adUser.Department -ne $adDepartment) {
            Set-ADUser -Identity $adUser -Department $adDepartment
            Write-Host "Opdaterede department for $($user.email) til $adDepartment"
            $comparisonResults += [PSCustomObject]@{ 
                Name            = $adUser.Name
                Email           = $user.email
                EmplyTitle      = $user.jobTitle
                ADTitle         = $adUser.Title
                EmplyDepartment = $adDepartment
                ADDepartment    = $adUser.Department
                ActionTaken     = "AD Department opdateret til: $adDepartment"
            }
        }

        # Opdater titel
        if ($adUser.Title -ne $user.jobTitle) {
            Set-ADUser -Identity $adUser -Title $user.jobTitle
            Write-Host "Opdaterede titel for $($user.email) til $($user.jobTitle)"
            $comparisonResults += [PSCustomObject]@{
                Name            = $adUser.Name
                Email           = $user.email
                EmplyTitle      = $user.jobTitle
                ADTitle         = $adUser.Title
                EmplyDepartment = $adDepartment
                ADDepartment    = $adUser.Department
                ActionTaken     = "AD Titel opdateret til: $($user.jobTitle)"
            }
        }
    
    }
}

# Sammen ændringerne og send mail
if ($comparisonResults.Count -gt 0) {
    $htmlTable = $comparisonResults | ConvertTo-Html -Fragment -Property Name, Email, EmplyTitle, ADTitle, EmplyDepartment, ADDepartment, ActionTaken
    $mailBody = @"
        <html>
        <head>
        <style>
            table, th, td {
                border: 1px solid black;
                border-collapse: collapse;
                padding: 5px;
            }
            th {
                background-color: #f2f2f2;
            }
        </style>
        </head>
        <body>
        <p>Følgende department ændringer er foretaget:</p>
        $htmlTable
        <p>Mvh IT-afdelingen</p>
        </body>
        </html>
"@
    # Send mail (tilpas parametre)
    Send-MailMessage -To $ToAddress -From $FromAddress -Subject "AD brugerændringer" -Body $mailBody -BodyAsHtml -SmtpServer $SMTPServer -Encoding UTF8
}