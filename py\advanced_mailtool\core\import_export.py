"""
Import/Export functionality for mbox files and individual emails
"""

import os
import email
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import zipfile
import tempfile
import shutil

from .email_model import EmailModel, EmailAttachment
from .mbox_parser import MboxParser


class EmailExporter:
    """Export emails to various formats"""
    
    def __init__(self):
        self.supported_formats = ['mbox', 'eml', 'json', 'html', 'txt']
    
    def export_single_email(self, email: EmailModel, output_path: Path, format: str = 'eml') -> bool:
        """Export a single email to file"""
        try:
            if format.lower() == 'eml':
                return self._export_eml(email, output_path)
            elif format.lower() == 'json':
                return self._export_json(email, output_path)
            elif format.lower() == 'html':
                return self._export_html(email, output_path)
            elif format.lower() == 'txt':
                return self._export_txt(email, output_path)
            else:
                raise ValueError(f"Unsupported format: {format}")
        
        except Exception as e:
            print(f"Failed to export email: {e}")
            return False
    
    def export_multiple_emails(self, emails: List[EmailModel], output_dir: Path, 
                             format: str = 'eml', include_attachments: bool = True) -> Dict[str, Any]:
        """Export multiple emails to directory"""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        results = {
            'total': len(emails),
            'successful': 0,
            'failed': 0,
            'errors': []
        }
        
        for i, email in enumerate(emails):
            try:
                # Generate safe filename
                safe_subject = self._sanitize_filename(email.subject or f"email_{i}")
                filename = f"{i+1:04d}_{safe_subject}.{format}"
                output_path = output_dir / filename
                
                if self.export_single_email(email, output_path, format):
                    results['successful'] += 1
                    
                    # Export attachments if requested
                    if include_attachments and email.has_attachments:
                        self._export_email_attachments(email, output_dir / f"{safe_subject}_attachments")
                else:
                    results['failed'] += 1
                    results['errors'].append(f"Failed to export email {i+1}: {email.subject}")
            
            except Exception as e:
                results['failed'] += 1
                results['errors'].append(f"Error exporting email {i+1}: {str(e)}")
        
        return results
    
    def export_to_mbox(self, emails: List[EmailModel], output_path: Path) -> bool:
        """Export emails to mbox format"""
        try:
            with open(output_path, 'w', encoding='utf-8', newline='') as f:
                for email in emails:
                    # Write mbox separator
                    from_line = f"From {email.sender} {email.date_str}\n"
                    f.write(from_line)
                    
                    # Write email content
                    email_content = str(email.message)
                    # Escape "From " lines in content
                    email_content = email_content.replace('\nFrom ', '\n>From ')
                    f.write(email_content)
                    f.write('\n\n')
            
            return True
        
        except Exception as e:
            print(f"Failed to export to mbox: {e}")
            return False
    
    def _export_eml(self, email: EmailModel, output_path: Path) -> bool:
        """Export email as EML file"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(str(email.message))
            return True
        except Exception:
            return False
    
    def _export_json(self, email: EmailModel, output_path: Path) -> bool:
        """Export email as JSON file"""
        try:
            email_data = {
                'message_id': email.message_id,
                'subject': email.subject,
                'sender': email.sender,
                'recipients': email.recipients,
                'cc': email.cc,
                'bcc': email.bcc,
                'date': email.date.isoformat() if email.date else None,
                'text_content': email.text_content,
                'html_content': email.html_content,
                'attachments': [
                    {
                        'filename': att.filename,
                        'content_type': att.content_type,
                        'size': att.size
                    }
                    for att in email.attachments
                ]
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(email_data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception:
            return False
    
    def _export_html(self, email: EmailModel, output_path: Path) -> bool:
        """Export email as HTML file"""
        try:
            html_content = email.html_content
            if not html_content:
                # Convert text to HTML
                text_content = email.text_content or "No content available"
                html_content = f"<pre>{text_content}</pre>"
            
            # Create complete HTML document
            full_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{email.subject}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ border-bottom: 1px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }}
                    .header-field {{ margin: 5px 0; }}
                    .label {{ font-weight: bold; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="header-field"><span class="label">Subject:</span> {email.subject}</div>
                    <div class="header-field"><span class="label">From:</span> {email.sender}</div>
                    <div class="header-field"><span class="label">To:</span> {email.recipients}</div>
                    <div class="header-field"><span class="label">Date:</span> {email.date_str}</div>
                </div>
                <div class="content">
                    {html_content}
                </div>
            </body>
            </html>
            """
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(full_html)
            
            return True
        except Exception:
            return False
    
    def _export_txt(self, email: EmailModel, output_path: Path) -> bool:
        """Export email as plain text file"""
        try:
            content = f"""Subject: {email.subject}
From: {email.sender}
To: {email.recipients}
Date: {email.date_str}

{email.text_content or "No text content available"}
"""
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
        except Exception:
            return False
    
    def _export_email_attachments(self, email: EmailModel, output_dir: Path):
        """Export all attachments from an email"""
        if not email.attachments:
            return
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for attachment in email.attachments:
            if attachment.is_cached:
                try:
                    safe_filename = self._sanitize_filename(attachment.filename)
                    attachment_path = output_dir / safe_filename
                    
                    with open(attachment_path, 'wb') as f:
                        f.write(attachment.data)
                
                except Exception as e:
                    print(f"Failed to export attachment {attachment.filename}: {e}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system operations"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        
        return filename or "unnamed"


class EmailImporter:
    """Import emails from various sources"""
    
    def __init__(self):
        self.supported_formats = ['mbox', 'eml']
    
    def import_from_mbox(self, mbox_path: Path) -> List[EmailModel]:
        """Import emails from mbox file"""
        try:
            with MboxParser(str(mbox_path)) as parser:
                return parser.parse_headers_only()
        except Exception as e:
            print(f"Failed to import from mbox: {e}")
            return []
    
    def import_from_eml_directory(self, directory: Path) -> List[EmailModel]:
        """Import emails from directory containing EML files"""
        emails = []
        
        for eml_file in directory.glob("*.eml"):
            try:
                email_model = self._import_eml_file(eml_file)
                if email_model:
                    emails.append(email_model)
            except Exception as e:
                print(f"Failed to import {eml_file}: {e}")
        
        return emails
    
    def import_single_eml(self, eml_path: Path) -> Optional[EmailModel]:
        """Import single EML file"""
        return self._import_eml_file(eml_path)
    
    def _import_eml_file(self, eml_path: Path) -> Optional[EmailModel]:
        """Import email from EML file"""
        try:
            with open(eml_path, 'r', encoding='utf-8') as f:
                message = email.message_from_file(f)
            
            return EmailModel(message, 0, str(eml_path))
        
        except Exception as e:
            print(f"Failed to import EML file {eml_path}: {e}")
            return None
    
    def merge_mbox_files(self, mbox_files: List[Path], output_path: Path) -> bool:
        """Merge multiple mbox files into one"""
        try:
            all_emails = []
            
            # Import from all mbox files
            for mbox_file in mbox_files:
                emails = self.import_from_mbox(mbox_file)
                all_emails.extend(emails)
            
            # Sort by date
            all_emails.sort(key=lambda e: e.date or datetime.min)
            
            # Export to new mbox file
            exporter = EmailExporter()
            return exporter.export_to_mbox(all_emails, output_path)
        
        except Exception as e:
            print(f"Failed to merge mbox files: {e}")
            return False


class ArchiveManager:
    """Manage email archives with compression"""
    
    def create_archive(self, emails: List[EmailModel], archive_path: Path, 
                      include_attachments: bool = True) -> bool:
        """Create compressed archive of emails"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Export emails to temporary directory
                exporter = EmailExporter()
                results = exporter.export_multiple_emails(
                    emails, temp_path / "emails", "eml", include_attachments
                )
                
                # Create metadata file
                metadata = {
                    'created': datetime.now().isoformat(),
                    'total_emails': len(emails),
                    'export_results': results,
                    'include_attachments': include_attachments
                }
                
                with open(temp_path / "metadata.json", 'w') as f:
                    json.dump(metadata, f, indent=2)
                
                # Create ZIP archive
                with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for file_path in temp_path.rglob('*'):
                        if file_path.is_file():
                            arcname = file_path.relative_to(temp_path)
                            zipf.write(file_path, arcname)
                
                return True
        
        except Exception as e:
            print(f"Failed to create archive: {e}")
            return False
    
    def extract_archive(self, archive_path: Path, extract_dir: Path) -> Dict[str, Any]:
        """Extract email archive"""
        try:
            extract_dir.mkdir(parents=True, exist_ok=True)
            
            with zipfile.ZipFile(archive_path, 'r') as zipf:
                zipf.extractall(extract_dir)
            
            # Read metadata if available
            metadata_file = extract_dir / "metadata.json"
            metadata = {}
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
            
            return {
                'success': True,
                'extract_dir': str(extract_dir),
                'metadata': metadata
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
