"""
High-performance mbox parser with memory mapping and lazy loading
"""

import email
import mmap
import os
import re
from pathlib import Path
from typing import List, Iterator, Optional, Tu<PERSON>, Dict
from email.message import EmailMessage
import threading
import time

from .email_model import EmailModel


class MboxParser:
    """High-performance mbox parser with memory mapping and caching"""
    
    def __init__(self, filepath: str, cache_dir: Optional[Path] = None):
        self.filepath = Path(filepath)
        self.cache_dir = cache_dir
        self._file_handle = None
        self._mmap = None
        self._email_offsets = []
        self._emails_cache = {}
        self._parsed = False
        self._lock = threading.RLock()
        
        # Regex for finding email boundaries (From lines)
        self._from_line_regex = re.compile(rb'^From .+', re.MULTILINE)
        
        if not self.filepath.exists():
            raise FileNotFoundError(f"Mbox file not found: {filepath}")
    
    def __enter__(self):
        self.open()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    def open(self):
        """Open mbox file with memory mapping"""
        if self._file_handle is not None:
            return
        
        try:
            self._file_handle = open(self.filepath, 'rb')
            if os.path.getsize(self.filepath) > 0:
                self._mmap = mmap.mmap(self._file_handle.fileno(), 0, access=mmap.ACCESS_READ)
        except Exception as e:
            self.close()
            raise RuntimeError(f"Failed to open mbox file: {e}")
    
    def close(self):
        """Close file handles and cleanup"""
        if self._mmap:
            self._mmap.close()
            self._mmap = None
        
        if self._file_handle:
            self._file_handle.close()
            self._file_handle = None
        
        self._emails_cache.clear()
    
    def _find_email_boundaries(self) -> List[int]:
        """Find all email boundaries in the mbox file"""
        if not self._mmap:
            return []
        
        boundaries = [0]  # First email starts at beginning
        
        # Find all "From " lines that mark email boundaries
        for match in self._from_line_regex.finditer(self._mmap):
            offset = match.start()
            if offset > 0:  # Skip the first one (already added)
                boundaries.append(offset)
        
        return boundaries
    
    def parse_headers_only(self) -> List[EmailModel]:
        """Parse only email headers for fast overview"""
        with self._lock:
            if self._parsed:
                return list(self._emails_cache.values())
            
            self.open()
            self._email_offsets = self._find_email_boundaries()
            
            emails = []
            for i, offset in enumerate(self._email_offsets):
                try:
                    email_model = self._parse_email_at_offset(offset, headers_only=True)
                    if email_model:
                        emails.append(email_model)
                        self._emails_cache[offset] = email_model
                except Exception as e:
                    print(f"Warning: Failed to parse email at offset {offset}: {e}")
                    continue
            
            self._parsed = True
            return emails
    
    def _parse_email_at_offset(self, offset: int, headers_only: bool = False) -> Optional[EmailModel]:
        """Parse email at specific offset"""
        if not self._mmap:
            return None
        
        try:
            # Find the end of this email (start of next email or end of file)
            next_offset = None
            current_index = self._email_offsets.index(offset)
            if current_index + 1 < len(self._email_offsets):
                next_offset = self._email_offsets[current_index + 1]
            else:
                next_offset = len(self._mmap)
            
            # Extract email data
            email_data = self._mmap[offset:next_offset]
            
            # Skip the "From " line if present
            lines = email_data.split(b'\n', 1)
            if len(lines) > 1 and lines[0].startswith(b'From '):
                email_data = lines[1]
            
            # Parse email
            if headers_only:
                # For headers-only parsing, we only need the header section
                header_end = email_data.find(b'\n\n')
                if header_end != -1:
                    email_data = email_data[:header_end + 2]
            
            message = email.message_from_bytes(email_data)
            return EmailModel(message, offset, str(self.filepath))
            
        except Exception as e:
            print(f"Error parsing email at offset {offset}: {e}")
            return None
    
    def get_email_at_index(self, index: int) -> Optional[EmailModel]:
        """Get email at specific index with full content loading"""
        if not self._parsed:
            self.parse_headers_only()
        
        if index < 0 or index >= len(self._email_offsets):
            return None
        
        offset = self._email_offsets[index]
        
        # Check if we have a cached version with full content
        if offset in self._emails_cache:
            cached_email = self._emails_cache[offset]
            if cached_email._content_loaded:
                return cached_email
        
        # Parse with full content
        email_model = self._parse_email_at_offset(offset, headers_only=False)
        if email_model:
            self._emails_cache[offset] = email_model
        
        return email_model
    
    def get_email_by_message_id(self, message_id: str) -> Optional[EmailModel]:
        """Find email by Message-ID"""
        if not self._parsed:
            self.parse_headers_only()
        
        for email_model in self._emails_cache.values():
            if email_model.message_id == message_id:
                return self.get_email_at_index(self._email_offsets.index(email_model.mbox_offset))
        
        return None
    
    def search_emails(self, query: str, fields: List[str] = None) -> List[EmailModel]:
        """Search emails by query in specified fields"""
        if not self._parsed:
            self.parse_headers_only()
        
        if fields is None:
            fields = ['subject', 'sender', 'recipients']
        
        query_lower = query.lower()
        results = []
        
        for email_model in self._emails_cache.values():
            match = False
            
            for field in fields:
                field_value = getattr(email_model, field, '').lower()
                if query_lower in field_value:
                    match = True
                    break
            
            if match:
                results.append(email_model)
        
        return results
    
    def get_emails_in_date_range(self, start_date, end_date) -> List[EmailModel]:
        """Get emails within date range"""
        if not self._parsed:
            self.parse_headers_only()
        
        results = []
        for email_model in self._emails_cache.values():
            if email_model.date and start_date <= email_model.date <= end_date:
                results.append(email_model)
        
        return results
    
    def get_statistics(self) -> Dict[str, any]:
        """Get mbox file statistics"""
        if not self._parsed:
            self.parse_headers_only()
        
        total_emails = len(self._emails_cache)
        file_size = self.filepath.stat().st_size
        
        # Count emails with attachments
        emails_with_attachments = 0
        for email_model in self._emails_cache.values():
            if email_model.has_attachments:
                emails_with_attachments += 1
        
        return {
            'total_emails': total_emails,
            'file_size_bytes': file_size,
            'file_size_mb': file_size / (1024 * 1024),
            'emails_with_attachments': emails_with_attachments,
            'filepath': str(self.filepath)
        }
    
    def __len__(self) -> int:
        """Get number of emails in mbox"""
        if not self._parsed:
            self.parse_headers_only()
        return len(self._email_offsets)
    
    def __iter__(self) -> Iterator[EmailModel]:
        """Iterate over all emails"""
        if not self._parsed:
            self.parse_headers_only()
        
        for i in range(len(self._email_offsets)):
            yield self.get_email_at_index(i)
    
    def __getitem__(self, index: int) -> EmailModel:
        """Get email by index"""
        email = self.get_email_at_index(index)
        if email is None:
            raise IndexError(f"Email index {index} out of range")
        return email
