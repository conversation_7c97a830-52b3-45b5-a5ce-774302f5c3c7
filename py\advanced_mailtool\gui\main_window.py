"""
Main window for the mbox reader/editor application
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, 
                               QSplitter, QMenuBar, QMenu, QToolBar, QStatusBar,
                               QFileDialog, QMessageBox, QProgressBar, QLabel,
                               QLineEdit, QPushButton, QFrame)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QAction, QIcon, QKeySequence

from .styles import DARK_STYLE
from .email_list_widget import EmailListWidget
from .email_viewer_widget import EmailViewerWidget
from .export_dialog import ExportDialog
from ..core.config import Config
from ..core.mbox_parser import MboxParser
from ..core.email_model import EmailModel
from ..core.import_export import EmailExporter, EmailImporter


class MboxLoadThread(QThread):
    """Thread for loading mbox files in background"""

    progress_updated = Signal(int, str)
    emails_loaded = Signal(list)
    error_occurred = Signal(str)
    
    def __init__(self, filepath: str, cache_dir: Path = None):
        super().__init__()
        self.filepath = filepath
        self.cache_dir = cache_dir
        self._parser = None
    
    def run(self):
        """Load mbox file in background"""
        try:
            self.progress_updated.emit(10, "Opening mbox file...")
            
            self._parser = MboxParser(self.filepath, self.cache_dir)
            self._parser.open()
            
            self.progress_updated.emit(30, "Parsing email headers...")
            
            emails = self._parser.parse_headers_only()
            
            self.progress_updated.emit(100, f"Loaded {len(emails)} emails")
            self.emails_loaded.emit(emails)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def get_parser(self) -> MboxParser:
        """Get the mbox parser instance"""
        return self._parser


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.current_parser = None
        self.current_emails = []
        self.load_thread = None
        
        self._setup_ui()
        self._setup_connections()
        self._apply_theme()
        self._restore_geometry()
    
    def _setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle("Advanced Mbox Reader")
        self.setMinimumSize(1000, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create toolbar
        self._create_toolbar()
        
        # Create search bar
        search_frame = self._create_search_bar()
        main_layout.addWidget(search_frame)
        
        # Create main content area
        content_splitter = self._create_content_area()
        main_layout.addWidget(content_splitter)
        
        # Create menu bar
        self._create_menu_bar()
        
        # Create status bar
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        open_action = QAction("&Open Mbox File...", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self._open_mbox_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # Recent files submenu
        self.recent_menu = file_menu.addMenu("Recent Files")
        self._update_recent_files_menu()
        
        file_menu.addSeparator()
        
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        refresh_action = QAction("&Refresh", self)
        refresh_action.setShortcut(QKeySequence.Refresh)
        refresh_action.triggered.connect(self._refresh_current_mbox)
        view_menu.addAction(refresh_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")
        
        export_action = QAction("&Export Emails...", self)
        export_action.triggered.connect(self._export_emails)
        tools_menu.addAction(export_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        about_action = QAction("&About", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_toolbar(self):
        """Create the toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setMovable(False)
        
        # Open file action
        open_action = QAction("Open", self)
        open_action.setToolTip("Open mbox file")
        open_action.triggered.connect(self._open_mbox_file)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # Refresh action
        refresh_action = QAction("Refresh", self)
        refresh_action.setToolTip("Refresh current mbox file")
        refresh_action.triggered.connect(self._refresh_current_mbox)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # Export action
        export_action = QAction("Export", self)
        export_action.setToolTip("Export emails")
        export_action.triggered.connect(self._export_emails)
        toolbar.addAction(export_action)
    
    def _create_search_bar(self) -> QFrame:
        """Create the search bar"""
        search_frame = QFrame()
        search_frame.setFrameStyle(QFrame.StyledPanel)
        search_frame.setMaximumHeight(50)
        
        layout = QHBoxLayout(search_frame)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Search label
        search_label = QLabel("Search:")
        layout.addWidget(search_label)
        
        # Search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search emails by subject, sender, or recipient...")
        self.search_input.textChanged.connect(self._on_search_changed)
        layout.addWidget(self.search_input)
        
        # Clear search button
        clear_button = QPushButton("Clear")
        clear_button.clicked.connect(self._clear_search)
        layout.addWidget(clear_button)
        
        return search_frame
    
    def _create_content_area(self) -> QSplitter:
        """Create the main content area with email list and viewer"""
        splitter = QSplitter(Qt.Horizontal)
        
        # Email list
        self.email_list = EmailListWidget()
        self.email_list.setMaximumWidth(400)
        splitter.addWidget(self.email_list)
        
        # Email viewer
        self.email_viewer = EmailViewerWidget()
        splitter.addWidget(self.email_viewer)
        
        # Set initial sizes
        splitter.setSizes([300, 700])
        
        return splitter
    
    def _create_status_bar(self):
        """Create the status bar"""
        self.status_bar = self.statusBar()
        
        # Email count label
        self.email_count_label = QLabel("No emails loaded")
        self.status_bar.addWidget(self.email_count_label)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_bar.addPermanentWidget(self.status_label)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.email_list.email_selected.connect(self._on_email_selected)
        self.email_list.email_double_clicked.connect(self._on_email_double_clicked)
    
    def _apply_theme(self):
        """Apply dark theme to the application"""
        self.setStyleSheet(DARK_STYLE)
    
    def _restore_geometry(self):
        """Restore window geometry from config"""
        geometry = self.config.get("window_geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        state = self.config.get("window_state")
        if state:
            self.restoreState(state)
    
    def _save_geometry(self):
        """Save window geometry to config"""
        self.config.set("window_geometry", self.saveGeometry())
        self.config.set("window_state", self.saveState())
        self.config.save()
    
    def _update_recent_files_menu(self):
        """Update recent files menu"""
        self.recent_menu.clear()
        
        recent_files = self.config.get("recent_files", [])
        for filepath in recent_files:
            if os.path.exists(filepath):
                action = QAction(os.path.basename(filepath), self)
                action.setToolTip(filepath)
                action.triggered.connect(lambda checked, path=filepath: self._load_mbox_file(path))
                self.recent_menu.addAction(action)
        
        if not recent_files:
            no_recent_action = QAction("No recent files", self)
            no_recent_action.setEnabled(False)
            self.recent_menu.addAction(no_recent_action)
    
    def _open_mbox_file(self):
        """Open mbox file dialog"""
        filepath, _ = QFileDialog.getOpenFileName(
            self,
            "Open Mbox File",
            "",
            "Mbox Files (*.mbox);;All Files (*)"
        )
        
        if filepath:
            self._load_mbox_file(filepath)
    
    def _load_mbox_file(self, filepath: str):
        """Load mbox file in background thread"""
        if self.load_thread and self.load_thread.isRunning():
            QMessageBox.warning(self, "Loading", "Another file is currently being loaded. Please wait.")
            return
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("Loading...")
        
        # Start loading thread
        self.load_thread = MboxLoadThread(filepath, self.config.cache_directory)
        self.load_thread.progress_updated.connect(self._on_load_progress)
        self.load_thread.emails_loaded.connect(self._on_emails_loaded)
        self.load_thread.error_occurred.connect(self._on_load_error)
        self.load_thread.start()
    
    def _on_load_progress(self, progress: int, message: str):
        """Handle load progress update"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
    
    def _on_emails_loaded(self, emails: list):
        """Handle emails loaded successfully"""
        self.current_emails = emails
        self.current_parser = self.load_thread.get_parser()
        
        # Update UI
        self.email_list.set_emails(emails)
        self._update_email_count()
        
        # Add to recent files
        self.config.add_recent_file(str(self.current_parser.filepath))
        self._update_recent_files_menu()
        
        # Hide progress
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready")
        
        # Update window title
        filename = os.path.basename(str(self.current_parser.filepath))
        self.setWindowTitle(f"Advanced Mbox Reader - {filename}")
    
    def _on_load_error(self, error_message: str):
        """Handle load error"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Error")
        
        QMessageBox.critical(self, "Error Loading Mbox", f"Failed to load mbox file:\n{error_message}")
    
    def _on_email_selected(self, email: EmailModel):
        """Handle email selection"""
        self.email_viewer.display_email(email)
    
    def _on_email_double_clicked(self, email: EmailModel):
        """Handle email double click"""
        # TODO: Open email in separate window or perform other action
        pass
    
    def _on_search_changed(self, query: str):
        """Handle search query change"""
        self.email_list.filter_emails(query)
        self._update_email_count()
    
    def _clear_search(self):
        """Clear search input"""
        self.search_input.clear()
    
    def _update_email_count(self):
        """Update email count in status bar"""
        if not self.current_emails:
            self.email_count_label.setText("No emails loaded")
            return
        
        total_count = self.email_list.get_email_count()
        filtered_count = self.email_list.get_filtered_count()
        
        if filtered_count == total_count:
            self.email_count_label.setText(f"{total_count} emails")
        else:
            self.email_count_label.setText(f"{filtered_count} of {total_count} emails")
    
    def _refresh_current_mbox(self):
        """Refresh current mbox file"""
        if self.current_parser:
            self._load_mbox_file(str(self.current_parser.filepath))
    
    def _export_emails(self):
        """Export emails dialog"""
        if not self.current_emails:
            QMessageBox.information(self, "Export", "No emails loaded to export.")
            return

        # Get selected emails or all emails
        selected_email = self.email_list.get_selected_email()
        if selected_email:
            emails_to_export = [selected_email]
            title = "Export Selected Email"
        else:
            emails_to_export = self.current_emails
            title = "Export All Emails"

        # Show export dialog
        dialog = ExportDialog(emails_to_export, title, self)
        dialog.exec()
    
    def _show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Advanced Mbox Reader",
            "Advanced Mbox Reader v1.0\n\n"
            "A modern, high-performance mbox file reader and editor\n"
            "built with PySide6 and Python.\n\n"
            "© 2025 Andersen Partners"
        )
    
    def closeEvent(self, event):
        """Handle window close event"""
        self._save_geometry()
        
        if self.current_parser:
            self.current_parser.close()
        
        if self.load_thread and self.load_thread.isRunning():
            self.load_thread.quit()
            self.load_thread.wait()
        
        event.accept()
