# Installation Guide - Advanced Mbox Reader

## Hurtig Start (Windows)

### Automatisk Installation
1. Dobbeltklik på `start.bat`
2. Scriptet vil automatisk:
   - Oprette et virtual environment
   - Installere alle dependencies
   - Køre tests
   - Starte applikationen

### Manuel Installation

#### Krav
- **Python 3.8 eller nyere** - Download fra [python.org](https://python.org)
- **Windows 10/11** (kan tilpasses til Linux/macOS)
- **Minimum 4GB RAM** anbefalet for store mbox filer

#### Trin-for-trin Installation

1. **Download og udpak projektet**
   ```
   Udpak til: C:\Users\<USER>\advanced_mailtool\
   ```

2. **Åbn Command Prompt eller PowerShell**
   - Tryk `Win + R`, skriv `cmd`, tryk Enter
   - Naviger til projekt mappen:
   ```cmd
   cd C:\Users\<USER>\advanced_mailtool
   ```

3. **Opret Virtual Environment (anbefalet)**
   ```cmd
   python -m venv venv
   venv\Scripts\activate
   ```

4. **Installer Dependencies**
   ```cmd
   pip install -r requirements.txt
   ```

5. **Test Installation**
   ```cmd
   python test_app.py
   ```

6. **Start Applikationen**
   ```cmd
   python main.py
   ```

## Dependencies Forklaring

### Core Dependencies
- **PySide6** - Modern GUI framework (Qt6 bindings)
- **email-validator** - Email validering
- **chardet** - Character encoding detection
- **Pillow** - Billede håndtering for attachments
- **beautifulsoup4** - HTML parsing
- **lxml** - XML/HTML parser
- **html2text** - HTML til text konvertering
- **psutil** - System performance monitoring

### Valgfrie Dependencies
- **python-magic** - Fil type detection (kun Linux/macOS)

## Fejlfinding

### Almindelige Problemer

#### "Python is not recognized"
**Problem**: Python er ikke installeret eller ikke i PATH
**Løsning**: 
1. Download Python fra python.org
2. Sørg for at markere "Add Python to PATH" under installation
3. Genstart Command Prompt

#### "No module named 'PySide6'"
**Problem**: PySide6 er ikke installeret korrekt
**Løsning**:
```cmd
pip install --upgrade PySide6
```

#### "Failed to create virtual environment"
**Problem**: Virtual environment kan ikke oprettes
**Løsning**:
```cmd
python -m pip install --upgrade pip
python -m pip install virtualenv
```

#### Applikationen starter ikke
**Problem**: Manglende dependencies eller fejl i koden
**Løsning**:
1. Kør test scriptet: `python test_app.py`
2. Læs fejlmeddelelserne og installer manglende pakker
3. Kontakt support hvis problemet fortsætter

### Performance Problemer

#### Langsom start
- **Årsag**: Store mbox filer eller lav RAM
- **Løsning**: Luk andre programmer, øg virtual memory

#### Høj memory brug
- **Årsag**: Mange emails indlæst samtidig
- **Løsning**: Ryd cache regelmæssigt, reducer preview limit

## Avanceret Konfiguration

### Cache Indstillinger
Rediger `~/.advanced_mailtool/config.json`:
```json
{
  "cache_enabled": true,
  "cache_size_mb": 500,
  "lazy_loading": true,
  "email_preview_limit": 1000
}
```

### Performance Tuning
For store mbox filer (>1GB):
```json
{
  "cache_size_mb": 1000,
  "email_preview_limit": 500,
  "attachment_preview_size": 10485760
}
```

## Systemkrav

### Minimum Krav
- **OS**: Windows 10, Linux (Ubuntu 18.04+), macOS 10.14+
- **RAM**: 2GB
- **Disk**: 500MB fri plads
- **Python**: 3.8+

### Anbefalet Krav
- **OS**: Windows 11, Linux (Ubuntu 20.04+), macOS 11+
- **RAM**: 8GB eller mere
- **Disk**: 2GB fri plads (for cache)
- **Python**: 3.10+

## Opdatering

### Opdater Dependencies
```cmd
pip install --upgrade -r requirements.txt
```

### Opdater Applikation
1. Download ny version
2. Backup din konfiguration fra `~/.advanced_mailtool/`
3. Erstat gamle filer
4. Kør `python test_app.py` for at verificere

## Afinstallation

### Fjern Applikation
1. Slet projekt mappen
2. Slet konfiguration: `~/.advanced_mailtool/`
3. Deaktiver virtual environment: `deactivate`
4. Slet virtual environment mappen

### Fjern Dependencies (valgfrit)
Hvis du brugte virtual environment:
```cmd
rmdir /s venv
```

## Support

### Log Filer
Applikationen logger til konsollen. For at gemme logs:
```cmd
python main.py > app.log 2>&1
```

### Rapporter Fejl
Inkluder følgende information:
- Windows version
- Python version (`python --version`)
- Fejlmeddelelse
- Log output
- Størrelse af mbox fil

### Kontakt
For support, kontakt IT-afdelingen med ovenstående information.
