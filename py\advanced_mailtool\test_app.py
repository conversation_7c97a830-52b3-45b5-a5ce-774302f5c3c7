#!/usr/bin/env python3
"""
Test script for the Advanced Mbox Reader application
"""

import sys
import os
import tempfile
import email
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def create_test_mbox():
    """Create a test mbox file with sample emails"""
    test_emails = [
        {
            'from': '<EMAIL>',
            'to': '<EMAIL>',
            'subject': 'Test Email 1',
            'body': 'This is the first test email with plain text content.',
            'date': 'Mon, 01 Jan 2024 10:00:00 +0000'
        },
        {
            'from': '<EMAIL>',
            'to': '<EMAIL>',
            'subject': 'Test Email 2 with HTML',
            'body': '<html><body><h1>HTML Email</h1><p>This is an <b>HTML</b> email with <a href="http://example.com">links</a>.</p></body></html>',
            'date': '<PERSON><PERSON>, 02 Jan 2024 11:30:00 +0000',
            'content_type': 'text/html'
        },
        {
            'from': '<EMAIL>',
            'to': '<EMAIL>',
            'subject': 'Test Email 3 - Long Subject Line That Should Be Truncated Properly in the Email List View',
            'body': '''This is a longer test email with multiple paragraphs.

This email contains:
- Multiple lines
- Special characters: åæø ÄÖÜ
- Unicode: 🚀 📧 ⭐

And some more content to test the preview functionality.''',
            'date': 'Wed, 03 Jan 2024 14:15:00 +0000'
        }
    ]
    
    # Create temporary mbox file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.mbox', delete=False, encoding='utf-8')
    
    for i, email_data in enumerate(test_emails):
        # Write mbox separator
        temp_file.write(f"From {email_data['from']} {email_data['date']}\n")
        
        # Write email headers
        temp_file.write(f"From: {email_data['from']}\n")
        temp_file.write(f"To: {email_data['to']}\n")
        temp_file.write(f"Subject: {email_data['subject']}\n")
        temp_file.write(f"Date: {email_data['date']}\n")
        temp_file.write(f"Message-ID: <test{i+1}@example.com>\n")
        
        content_type = email_data.get('content_type', 'text/plain')
        temp_file.write(f"Content-Type: {content_type}; charset=utf-8\n")
        temp_file.write("MIME-Version: 1.0\n")
        temp_file.write("\n")  # Empty line separates headers from body
        
        # Write email body
        temp_file.write(email_data['body'])
        temp_file.write("\n\n")  # Two newlines separate emails
    
    temp_file.close()
    return temp_file.name


def test_core_functionality():
    """Test core functionality without GUI"""
    print("Testing core functionality...")
    
    try:
        # Test imports
        from core.mbox_parser import MboxParser
        from core.email_model import EmailModel
        from core.config import Config
        from core.import_export import EmailExporter
        print("✓ All core modules imported successfully")
        
        # Create test mbox file
        test_mbox_path = create_test_mbox()
        print(f"✓ Created test mbox file: {test_mbox_path}")
        
        # Test mbox parsing
        with MboxParser(test_mbox_path) as parser:
            emails = parser.parse_headers_only()
            print(f"✓ Parsed {len(emails)} emails from mbox file")
            
            # Test email content loading
            if emails:
                first_email = parser.get_email_at_index(0)
                if first_email:
                    print(f"✓ Loaded first email: '{first_email.subject}'")
                    print(f"  - Sender: {first_email.sender}")
                    print(f"  - Preview: {first_email.preview_text[:50]}...")
                    
                    # Test content loading
                    text_content = first_email.text_content
                    html_content = first_email.html_content
                    print(f"  - Text content length: {len(text_content)}")
                    print(f"  - HTML content length: {len(html_content)}")
        
        # Test configuration
        config = Config()
        print(f"✓ Configuration loaded, cache dir: {config.cache_directory}")
        
        # Test export functionality
        exporter = EmailExporter()
        temp_dir = Path(tempfile.mkdtemp())
        
        # Export first email as EML
        if emails:
            eml_path = temp_dir / "test_email.eml"
            success = exporter.export_single_email(emails[0], eml_path, 'eml')
            if success:
                print(f"✓ Exported email to EML: {eml_path}")
            else:
                print("✗ Failed to export email to EML")
        
        # Cleanup
        os.unlink(test_mbox_path)
        print("✓ Cleaned up test files")
        
        return True
        
    except Exception as e:
        print(f"✗ Core functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_imports():
    """Test GUI module imports"""
    print("\nTesting GUI imports...")
    
    try:
        # Test PySide6 availability
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtWebEngineWidgets import QWebEngineView
        print("✓ PySide6 modules imported successfully")
        
        # Test GUI modules
        from gui.main_window import MainWindow
        from gui.email_list_widget import EmailListWidget
        from gui.email_viewer_widget import EmailViewerWidget
        from gui.export_dialog import ExportDialog
        from gui.styles import DARK_STYLE
        print("✓ All GUI modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ GUI import test failed: {e}")
        print("  Make sure PySide6 is installed: pip install PySide6")
        return False
    except Exception as e:
        print(f"✗ GUI import test failed: {e}")
        return False


def test_dependencies():
    """Test all required dependencies"""
    print("\nTesting dependencies...")
    
    required_packages = [
        'PySide6',
        'email_validator', 
        'chardet',
        'Pillow',
        'beautifulsoup4',
        'lxml',
        'html2text',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def main():
    """Run all tests"""
    print("Advanced Mbox Reader - Test Suite")
    print("=" * 40)
    
    all_tests_passed = True
    
    # Test dependencies
    if not test_dependencies():
        all_tests_passed = False
    
    # Test GUI imports
    if not test_gui_imports():
        all_tests_passed = False
    
    # Test core functionality
    if not test_core_functionality():
        all_tests_passed = False
    
    print("\n" + "=" * 40)
    if all_tests_passed:
        print("🎉 All tests passed! The application should work correctly.")
        print("\nTo start the application, run:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please fix the issues before running the application.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
