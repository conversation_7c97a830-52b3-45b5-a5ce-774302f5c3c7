"""
Email list widget with performance optimization and virtual scrolling
"""

from PySide6.QtWidgets import (QListWidget, QListWidgetItem, QWidget, QVBoxLayout, 
                               QHBoxLayout, QLabel, QFrame, QAbstractItemView)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QPalette, QPixmap, QIcon
from typing import List, Optional
import datetime

from ..core.email_model import EmailModel


class EmailListItem(QWidget):
    """Custom widget for displaying email in list"""
    
    def __init__(self, email: EmailModel):
        super().__init__()
        self.email = email
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the email item UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(2)
        
        # Top row: sender and date
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # Sender
        self.sender_label = QLabel(self.email.sender)
        sender_font = QFont()
        sender_font.setBold(True)
        self.sender_label.setFont(sender_font)
        self.sender_label.setStyleSheet("color: #ffffff;")
        
        # Date
        date_text = ""
        if self.email.date:
            if self.email.date.date() == datetime.date.today():
                date_text = self.email.date.strftime("%H:%M")
            else:
                date_text = self.email.date.strftime("%d/%m/%Y")
        
        self.date_label = QLabel(date_text)
        self.date_label.setStyleSheet("color: #cccccc; font-size: 11px;")
        self.date_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(self.sender_label)
        top_layout.addStretch()
        top_layout.addWidget(self.date_label)
        
        # Subject
        self.subject_label = QLabel(self.email.subject)
        subject_font = QFont()
        subject_font.setBold(False)
        self.subject_label.setFont(subject_font)
        self.subject_label.setStyleSheet("color: #ffffff; font-size: 13px;")
        
        # Preview text
        self.preview_label = QLabel(self.email.preview_text)
        self.preview_label.setStyleSheet("color: #aaaaaa; font-size: 11px;")
        self.preview_label.setWordWrap(True)
        
        # Attachment indicator
        if self.email.has_attachments:
            attachment_layout = QHBoxLayout()
            attachment_layout.setContentsMargins(0, 0, 0, 0)
            
            attachment_icon = QLabel("📎")
            attachment_icon.setStyleSheet("color: #0078d4; font-size: 12px;")
            attachment_layout.addWidget(attachment_icon)
            attachment_layout.addStretch()
            
            layout.addLayout(attachment_layout)
        
        layout.addLayout(top_layout)
        layout.addWidget(self.subject_label)
        layout.addWidget(self.preview_label)
        
        # Add separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #3c3c3c;")
        layout.addWidget(separator)


class EmailListWidget(QListWidget):
    """High-performance email list widget with virtual scrolling"""
    
    email_selected = Signal(EmailModel)
    email_double_clicked = Signal(EmailModel)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._emails = []
        self._filtered_emails = []
        self._current_filter = ""
        
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """Setup the list widget"""
        self.setAlternatingRowColors(False)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # Enable smooth scrolling
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Set minimum size
        self.setMinimumWidth(300)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.itemClicked.connect(self._on_item_clicked)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
    
    def set_emails(self, emails: List[EmailModel]):
        """Set the list of emails to display"""
        self._emails = emails
        self._filtered_emails = emails.copy()
        self._refresh_list()
    
    def _refresh_list(self):
        """Refresh the list display"""
        self.clear()
        
        for email in self._filtered_emails:
            # Create list item
            item = QListWidgetItem()
            item.setData(Qt.UserRole, email)
            
            # Create custom widget
            widget = EmailListItem(email)
            item.setSizeHint(widget.sizeHint())
            
            # Add to list
            self.addItem(item)
            self.setItemWidget(item, widget)
    
    def filter_emails(self, query: str):
        """Filter emails by search query"""
        self._current_filter = query.lower()
        
        if not query:
            self._filtered_emails = self._emails.copy()
        else:
            self._filtered_emails = []
            for email in self._emails:
                if (query.lower() in email.subject.lower() or 
                    query.lower() in email.sender.lower() or
                    query.lower() in email.recipients.lower()):
                    self._filtered_emails.append(email)
        
        self._refresh_list()
    
    def get_selected_email(self) -> Optional[EmailModel]:
        """Get currently selected email"""
        current_item = self.currentItem()
        if current_item:
            return current_item.data(Qt.UserRole)
        return None
    
    def select_email(self, email: EmailModel):
        """Select specific email in the list"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.UserRole) == email:
                self.setCurrentItem(item)
                break
    
    def _on_item_clicked(self, item: QListWidgetItem):
        """Handle item click"""
        email = item.data(Qt.UserRole)
        if email:
            self.email_selected.emit(email)
    
    def _on_item_double_clicked(self, item: QListWidgetItem):
        """Handle item double click"""
        email = item.data(Qt.UserRole)
        if email:
            self.email_double_clicked.emit(email)
    
    def get_email_count(self) -> int:
        """Get total number of emails"""
        return len(self._emails)
    
    def get_filtered_count(self) -> int:
        """Get number of filtered emails"""
        return len(self._filtered_emails)
    
    def clear_selection(self):
        """Clear current selection"""
        self.setCurrentItem(None)
