"""
Dark mode styles for the mbox reader/editor
"""

DARK_STYLE = """
/* Main application styling */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* Menu bar */
QMenuBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border: none;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    margin: 1px;
    border-radius: 3px;
}

QMenuBar::item:selected {
    background-color: #4a4a4a;
}

QMenuBar::item:pressed {
    background-color: #5a5a5a;
}

/* Menu */
QMenu {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 2px;
}

QMenu::item {
    padding: 6px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #4a4a4a;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 2px 0px;
}

/* Toolbar */
QToolBar {
    background-color: #3c3c3c;
    border: none;
    spacing: 2px;
    padding: 2px;
}

QToolButton {
    background-color: transparent;
    border: none;
    padding: 6px;
    margin: 1px;
    border-radius: 3px;
    color: #ffffff;
}

QToolButton:hover {
    background-color: #4a4a4a;
}

QToolButton:pressed {
    background-color: #5a5a5a;
}

QToolButton:checked {
    background-color: #0078d4;
}

/* Status bar */
QStatusBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-top: 1px solid #555555;
}

/* Splitter */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* List widget (email list) */
QListWidget {
    background-color: #2b2b2b;
    color: #ffffff;
    border: 1px solid #555555;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    outline: none;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #3c3c3c;
}

QListWidget::item:selected {
    background-color: #0078d4;
}

QListWidget::item:hover {
    background-color: #4a4a4a;
}

/* Tree widget */
QTreeWidget {
    background-color: #2b2b2b;
    color: #ffffff;
    border: 1px solid #555555;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    outline: none;
}

QTreeWidget::item {
    padding: 4px;
    border-bottom: 1px solid #3c3c3c;
}

QTreeWidget::item:selected {
    background-color: #0078d4;
}

QTreeWidget::item:hover {
    background-color: #4a4a4a;
}

/* Text edit (email content) */
QTextEdit {
    background-color: #2b2b2b;
    color: #ffffff;
    border: 1px solid #555555;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
}

/* Web engine view (HTML emails) */
QWebEngineView {
    background-color: #2b2b2b;
    border: 1px solid #555555;
}

/* Scroll bars */
QScrollBar:vertical {
    background-color: #3c3c3c;
    width: 12px;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #5a5a5a;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #6a6a6a;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #3c3c3c;
    height: 12px;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #5a5a5a;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #6a6a6a;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

/* Buttons */
QPushButton {
    background-color: #4a4a4a;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 6px 12px;
    border-radius: 3px;
}

QPushButton:hover {
    background-color: #5a5a5a;
}

QPushButton:pressed {
    background-color: #6a6a6a;
}

QPushButton:disabled {
    background-color: #3c3c3c;
    color: #888888;
}

/* Line edit */
QLineEdit {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 4px;
    border-radius: 3px;
}

QLineEdit:focus {
    border-color: #0078d4;
}

/* Combo box */
QComboBox {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 4px;
    border-radius: 3px;
}

QComboBox:hover {
    border-color: #0078d4;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik01IDZMMCAwSDEwTDUgNloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+);
}

QComboBox QAbstractItemView {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    selection-background-color: #0078d4;
}

/* Progress bar */
QProgressBar {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 3px;
    text-align: center;
    color: #ffffff;
}

QProgressBar::chunk {
    background-color: #0078d4;
    border-radius: 2px;
}

/* Group box */
QGroupBox {
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 3px;
    margin-top: 10px;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}

/* Tab widget */
QTabWidget::pane {
    border: 1px solid #555555;
    background-color: #2b2b2b;
}

QTabBar::tab {
    background-color: #3c3c3c;
    color: #ffffff;
    padding: 6px 12px;
    margin-right: 2px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

QTabBar::tab:selected {
    background-color: #0078d4;
}

QTabBar::tab:hover {
    background-color: #4a4a4a;
}

/* Dialog */
QDialog {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* Label */
QLabel {
    color: #ffffff;
}

/* Header view */
QHeaderView::section {
    background-color: #3c3c3c;
    color: #ffffff;
    padding: 6px;
    border: none;
    border-right: 1px solid #555555;
    border-bottom: 1px solid #555555;
}

QHeaderView::section:hover {
    background-color: #4a4a4a;
}
"""