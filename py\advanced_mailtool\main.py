#!/usr/bin/env python3
"""
Advanced Mbox Reader/Editor <PERSON><PERSON>
Modern GUI application for reading and editing mbox files with PySide6
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from gui.main_window import MainWindow
from core.config import Config


def setup_application():
    """Setup the QApplication with proper settings"""
    app = QApplication(sys.argv)
    app.setApplicationName("Advanced Mbox Reader")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Andersen Partners")
    
    # Enable high DPI scaling
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app


def main():
    """Main entry point"""
    app = setup_application()
    
    # Load configuration
    config = Config()
    
    # Create and show main window
    window = MainWindow(config)
    window.show()
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
